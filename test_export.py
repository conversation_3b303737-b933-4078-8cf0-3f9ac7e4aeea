#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导出接口
"""

import requests
import json
from datetime import datetime

def test_export():
    """测试导出功能"""
    
    # 后端接口地址
    url = "http://localhost:9091/api/admin/xzp/txn/exportTxnLogDetail"
    
    # 请求参数
    data = {
        "startTime": "20250701",
        "endTime": "20250805"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/octet-stream"
    }
    
    try:
        print(f"正在调用导出接口: {url}")
        print(f"请求参数: {json.dumps(data, ensure_ascii=False)}")
        
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=60)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            # 保存Excel文件
            filename = f"缴费明细导出_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xls"
            with open(filename, 'wb') as f:
                f.write(response.content)
            print(f"导出成功，文件保存为: {filename}")
            print(f"文件大小: {len(response.content)} 字节")
        else:
            print(f"导出失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except Exception as e:
        print(f"其他异常: {e}")

if __name__ == '__main__':
    test_export()
