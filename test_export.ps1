# 测试导出接口
$url = "http://localhost:9091/api/admin/xzp/txn/exportTxnLogDetail"
$body = @{
    startTime = "20250701"
    endTime = "20250805"
    bngDate = "20250701"
    endDate = "20250805"
} | ConvertTo-Json

$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/octet-stream"
}

try {
    Write-Host "正在调用导出接口: $url"
    Write-Host "请求参数: $body"
    
    $response = Invoke-RestMethod -Uri $url -Method Post -Body $body -Headers $headers -TimeoutSec 60
    
    Write-Host "导出成功"
    Write-Host "响应类型: $($response.GetType())"
    
    if ($response -is [byte[]]) {
        $filename = "缴费明细导出_$(Get-Date -Format 'yyyyMMdd_HHmmss').xls"
        [System.IO.File]::WriteAllBytes($filename, $response)
        Write-Host "文件保存为: $filename"
        Write-Host "文件大小: $($response.Length) 字节"
    } else {
        Write-Host "响应内容: $response"
    }
}
catch {
    Write-Host "请求失败: $($_.Exception.Message)"
    Write-Host "详细错误: $($_.Exception)"
}
