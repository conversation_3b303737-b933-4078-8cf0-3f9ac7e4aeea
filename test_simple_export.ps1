# 简单测试导出接口
$url = "http://localhost:9091/api/admin/xzp/txn/exportTxnLogDetail"
$body = @{
    startTime = "20250701"
    endTime = "20250805"
} | ConvertTo-Json

$headers = @{
    "Content-Type" = "application/json"
}

try {
    Write-Host "正在调用导出接口: $url"
    Write-Host "请求参数: $body"
    
    $response = Invoke-WebRequest -Uri $url -Method Post -Body $body -Headers $headers -TimeoutSec 60
    
    Write-Host "导出成功"
    Write-Host "响应状态: $($response.StatusCode)"
    Write-Host "响应类型: $($response.Headers.'Content-Type')"
    
    if ($response.Headers.'Content-Type' -like "*excel*" -or $response.Headers.'Content-Type' -like "*octet-stream*") {
        $filename = "缴费明细导出_$(Get-Date -Format 'yyyyMMdd_HHmmss').xls"
        [System.IO.File]::WriteAllBytes($filename, $response.Content)
        Write-Host "文件保存为: $filename"
        Write-Host "文件大小: $($response.Content.Length) 字节"
    } else {
        Write-Host "响应内容: $($response.Content)"
    }
}
catch {
    Write-Host "导出失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "HTTP状态码: $($_.Exception.Response.StatusCode)"
        Write-Host "响应内容: $($_.Exception.Response.Content)"
    }
}
