# 测试直接导出功能
$headers = @{
    'Content-Type' = 'application/json'
}

$body = @{
    setdate = "20250701"
} | ConvertTo-Json

try {
    Write-Host "测试缴费明细导出接口..."
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/xzp/txn/exportTxnLogDetail" -Method POST -Headers $headers -Body $body -OutFile "test_export_result.xls"
    Write-Host "导出成功，文件已保存为 test_export_result.xls"
} catch {
    Write-Host "导出失败: $($_.Exception.Message)"
    Write-Host "响应内容: $($_.Exception.Response)"
}
