#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建缴费明细导出模板的脚本
"""

import xlwt
import os

def create_txn_log_detail_template():
    """创建缴费明细导出模板"""
    
    # 创建工作簿
    workbook = xlwt.Workbook(encoding='utf-8')
    worksheet = workbook.add_sheet('缴费明细')
    
    # 设置表头样式
    header_style = xlwt.XFStyle()
    header_font = xlwt.Font()
    header_font.bold = True
    header_font.name = '宋体'
    header_style.font = header_font
    
    # 设置对齐方式
    alignment = xlwt.Alignment()
    alignment.horz = xlwt.Alignment.HORZ_CENTER
    alignment.vert = xlwt.Alignment.VERT_CENTER
    header_style.alignment = alignment
    
    # 表头数据
    headers = [
        '银联清算日期',
        '业务代码', 
        '委托代码',
        '子业务代码',
        '子业务',
        '应付笔数',
        '应付金额(元)',
        '应收笔数', 
        '应收金额(元)',
        '轧差金额(元)'
    ]
    
    # 写入表头
    for col, header in enumerate(headers):
        worksheet.write(0, col, header, header_style)
    
    # EasyPOI模板行数据
    template_data = [
        '{{fe:txnLogDetailList t.setdate}}',
        '{{t.str}}',
        '{{t.merchid}}', 
        '{{t.str30}}',
        '{{t.str31}}',
        '{{t.num}}',
        '{{t.amt/100}}',
        '{{t.receivableNum}}',
        '{{t.receivableAmt/100}}',
        '{{t.netAmt}}'
    ]
    
    # 写入模板行
    for col, template in enumerate(template_data):
        worksheet.write(1, col, template)
    
    # 设置列宽
    col_widths = [3000, 4000, 3000, 3000, 3000, 2500, 3500, 2500, 3500, 3500]
    for i, width in enumerate(col_widths):
        worksheet.col(i).width = width
    
    # 保存文件
    output_path = r'c:\workspace\psbc-work\xm-xzp-0.0.0.1\xm-xzp-impl\src\main\resources\doc\xzp\缴费明细导出模板.xls'
    
    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    workbook.save(output_path)
    print(f"模板文件已创建: {output_path}")

if __name__ == '__main__':
    create_txn_log_detail_template()
