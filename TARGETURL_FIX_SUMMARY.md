# targetUrl 参数获取问题修复总结

## 问题描述

在前端项目 yoaf-web-******* 中，以下两个页面在点击查询时无法正确获取 `targetUrl` 参数：

1. `src/views/xzp/depositnotice/index.vue` - 存款通知查询页面
2. `src/views/xzp/cardchange/index.vue` - 卡变更通知查询页面

而 `src/views/xzp/acctquery/index.vue` 页面能够正常获取 `targetUrl` 参数。

## 问题原因分析

通过对比三个页面的代码，发现了数据字典访问方式的差异：

### 正确的访问方式（acctquery 中使用）
```javascript
const targetUrl = this.dict.label.xzp_wl_url['w0000'] || '';
```

### 错误的访问方式
1. **depositnotice 页面**（已修复）：
```javascript
// 修复前
const targetUrl = this.dict.xzp_wl_url && this.dict.xzp_wl_url.length > 0 
  ? this.dict.xzp_wl_url.find(item => item.dictLabel === 'w0002')?.dictValue 
  : null;

// 修复后
const targetUrl = this.dict.label.xzp_wl_url['w0002'] || '';
```

2. **cardchange 页面**（格式正确但添加了调试信息）：
```javascript
const targetUrl = this.dict.label.xzp_wl_url['w0001'] || '';
```

## 数据字典系统工作原理

项目使用了自定义的数据字典系统，主要组件包括：

1. **字典混入 (Mixin)**：`src/utils/dict/index.js`
2. **字典类**：`src/utils/dict/Dict.js`
3. **字典数据转换器**：`src/utils/dict/DictConverter.js`
4. **字典配置**：`src/components/DictData/index.js`

### 字典数据结构

字典系统会将数据转换为以下结构：
```javascript
this.dict = {
  label: {
    xzp_wl_url: {
      'w0000': 'http://127.0.0.1:8445/api/xzp/50002',
      'w0001': 'http://127.0.0.1:8445/api/xzp/50003',
      'w0002': 'http://127.0.0.1:8445/api/xzp/50004'
    }
  },
  type: {
    xzp_wl_url: [
      // DictData 对象数组
    ]
  }
}
```

## 修复内容

### 1. 修复 depositnotice/index.vue

**文件位置**：`src/views/xzp/depositnotice/index.vue`

**修改内容**：
- 修复了 `targetUrl` 的获取方式（第127-128行）
- 添加了调试日志（第126-130行）
- 添加了字典加载完成回调方法（第117-120行）

### 2. 优化 cardchange/index.vue

**文件位置**：`src/views/xzp/cardchange/index.vue`

**修改内容**：
- 添加了调试日志（第194-198行）
- 添加了字典加载完成回调方法（第94-97行）

## 字典配置对应关系

根据截图中的数据字典配置：

| 字典键 | 字典值 | 用途 |
|--------|--------|------|
| w0000 | http://127.0.0.1:8445/api/xzp/50002 | acctquery 账户查询 |
| w0001 | http://127.0.0.1:8445/api/xzp/50003 | cardchange 卡变更通知 |
| w0002 | http://127.0.0.1:8445/api/xzp/50004 | depositnotice 存款通知 |

## 测试验证

### 启动项目
```bash
npm run dev
```

### 验证步骤
1. 访问存款通知查询页面：`/xzp/depositnotice`
2. 访问卡变更通知查询页面：`/xzp/cardchange`
3. 打开浏览器开发者工具查看控制台日志
4. 点击查询按钮，检查是否能正确获取 `targetUrl` 参数

### 预期结果
- 控制台应该显示字典加载完成的日志
- 控制台应该显示正确的 `targetUrl` 值
- 查询请求应该包含正确的 `targetUrl` 参数

## 注意事项

1. **字典加载时机**：字典数据是异步加载的，需要等待 `onDictReady` 回调执行完成
2. **字典访问格式**：必须使用 `this.dict.label.字典类型[字典键]` 的格式
3. **调试信息**：生产环境部署时可以移除添加的 `console.log` 调试信息

## 相关文件

- `src/views/xzp/depositnotice/index.vue` - 存款通知查询页面
- `src/views/xzp/cardchange/index.vue` - 卡变更通知查询页面  
- `src/views/xzp/acctquery/index.vue` - 账户查询页面（参考实现）
- `src/utils/dict/` - 字典系统核心文件
- `src/components/DictData/index.js` - 字典数据配置
