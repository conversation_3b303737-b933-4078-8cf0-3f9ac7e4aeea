# XMWEB_XMPLDSF_A004 交易服务业务分析文档

## 1. 服务概述

### 基本信息
- **交易代码**: XMWEB_XMPLDSF_A004
- **业务名称**: 人工换卡
- **业务描述**: 处理用户银行卡更换业务，维护签约关系的连续性
- **传输通道**: bran_prov3502_xmweb
- **业务流程**: _inbound.prov3502_xmpldsf.XMWEB_XMPLDSF_A004.XMWEB_XMPLDSF_A004
- **创建时间**: 2021-05-08
- **消息格式**: JSON

## 2. 接口定义

### 2.1 请求消息结构 (XMWEB_XMPLDSF_A004REQ)

#### 消息头 (head) - 基于msg_head_Q
| 字段名 | 显示名称 | 类型 | 长度 | 必填 | 说明 |
|--------|----------|------|------|------|------|
| merchid | 委托单位代码 | String | 12 | 是 | 标识委托单位 |
| opecd | 业务代码 | String | 6 | 是 | 业务类型标识 |
| busikind | 业务类型 | String | 20 | 否 | 业务分类 |
| tradecode | 交易代码 | String | 20 | 是 | 固定值：XMWEB_XMPLDSF_A004 |
| sendseqno | 发送方流水号 | String | 50 | 是 | 唯一标识本次交易 |
| empname | 账户名称 | String | 50 | 否 | 操作员或账户名称 |
| empcode | 卡号 | String | 30 | 否 | 操作员卡号 |
| orgcode | 机构代码 | String | 9 | 是 | 发起机构代码 |

#### 消息体 (body)
| 字段名 | 显示名称 | 类型 | 必填 | 说明 |
|--------|----------|------|------|------|
| payid | 用户号 | String | 是 | 需要换卡的用户标识 |
| str31 | 旧卡号 | String | 是 | 原有的银行卡号 |
| account | 新卡号 | String | 是 | 新的银行卡号 |

### 2.2 响应消息结构 (XMWEB_XMPLDSF_A004RES)

#### 消息头 (head) - 基于msg_head_R
| 字段名 | 显示名称 | 类型 | 说明 |
|--------|----------|------|------|
| rescode | 响应代码 | String | 处理结果代码 |
| resmsg | 响应消息 | String | 处理结果描述 |
| resseqno | 响应方流水号 | String | 响应流水号 |
| restime | 响应方时间 | String | 响应时间戳 |
| sendseqno | 发送方流水号 | String | 原请求流水号 |

## 3. 业务逻辑流程

### 3.1 主要处理步骤

```mermaid
graph TD
    A[接收换卡请求] --> B[提取请求参数]
    B --> C[验证必填参数]
    C --> D[查询用户签约信息]
    D --> E{用户是否已签约?}
    E -->|是| F[更新旧卡状态为注销]
    F --> G[新增新卡签约记录]
    G --> H[返回成功响应]
    E -->|否| I[返回用户未签约错误]
    D --> J{查询异常?}
    J -->|是| K[返回系统错误]
```

### 3.2 核心业务逻辑

1. **参数提取与验证**
   - 从请求消息中提取委托单位代码(MERCH_ID)、业务代码(OPE_CD)
   - 提取用户号(PAY_ID)、旧卡号(STR31)、新卡号(ACCOUNT)

2. **签约状态查询**
   - 查询tb_pay_commi_info表验证用户签约状态
   - 查询条件：merch_id + ope_cd + pay_id + acc_card_id(旧卡号)

3. **换卡处理**
   - 如果查询到签约记录：
     - 将旧卡签约状态更新为"3"（注销状态）
     - 复制原签约信息，创建新卡签约记录
     - 新卡签约状态设置为"1"（有效状态）
     - 更新时间戳为当前时间

4. **响应处理**
   - 成功：RET_CODE="0000", RET_EXPLAIN="交易成功"
   - 失败：RET_CODE="0001", RET_EXPLAIN="该用户未签约"
   - 异常：RET_CODE="9999", RET_EXPLAIN="系统错误"

## 4. 数据库操作

### 4.1 涉及的数据表

#### tb_pay_commi_info (签约信息表)
| 字段名 | 说明 | 类型 |
|--------|------|------|
| merch_id | 委托单位代码 | String |
| ope_cd | 业务代码 | String |
| pay_id | 用户号 | String |
| acc_card_id | 银行卡号 | String |
| stat_cd | 签约状态 | String |
| commi_dt | 签约日期 | String |
| effect_dt | 生效日期 | String |
| time_stamp | 时间戳 | String |
| acc_name | 账户名称 | String |
| sign_name | 签约姓名 | String |
| phone_id | 手机号 | String |

### 4.2 SQL操作

#### 查询签约信息
```sql
SELECT merch_id, ope_cd, pay_id, sub_key, corp_cd, payer_id, 
       acc_id, acc_card_id, acc_name, acc_bank_flag, card_pk_fg,
       sign_name, phone_id, commi_dt, effect_dt, time_stamp, 
       stat_cd, mark1, paper_id
FROM tb_pay_commi_info
WHERE merch_id = ? AND ope_cd = ? AND pay_id = ? AND acc_card_id = ?
```

#### 更新旧卡状态
```sql
UPDATE tb_pay_commi_info 
SET stat_cd = '3'
WHERE merch_id = ? AND ope_cd = ? AND pay_id = ? AND acc_card_id = ?
```

#### 新增新卡签约记录
```sql
INSERT INTO tb_pay_commi_info(
    merch_id, ope_cd, pay_id, sub_key, corp_cd, payer_id,
    acc_id, acc_card_id, acc_name, acc_bank_flag, card_pk_fg,
    sign_name, phone_id, commi_dt, effect_dt, time_stamp,
    stat_cd, mark1, paper_id
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, '1', ?, ?)
```

## 5. 异常处理

### 5.1 异常处理机制
- **正常流程异常**: 通过errorCheck方法处理，返回系统错误
- **业务逻辑异常**: 在主业务方法中捕获并处理
- **数据库异常**: 通过try-catch机制捕获数据库操作异常

### 5.2 错误码定义
| 错误码 | 错误描述 | 处理建议 |
|--------|----------|----------|
| 0000 | 交易成功 | 正常处理完成 |
| 0001 | 该用户未签约 | 检查用户签约状态 |
| 9999 | 系统错误 | 检查系统日志，联系技术支持 |

## 6. 调用示例

### 6.1 请求示例
```json
{
  "head": {
    "merchid": "************",
    "opecd": "ABC001",
    "tradecode": "XMWEB_XMPLDSF_A004",
    "sendseqno": "20240805001234567890",
    "orgcode": "*********"
  },
  "body": {
    "payid": "USER123456",
    "str31": "****************",
    "account": "****************"
  }
}
```

### 6.2 响应示例
```json
{
  "head": {
    "rescode": "0000",
    "resmsg": "交易成功",
    "resseqno": "20240805001234567891",
    "restime": "**************",
    "sendseqno": "20240805001234567890"
  }
}
```

## 7. 技术实现

### 7.1 技术架构
- **开发框架**: Primeton BTP
- **数据库**: 关系型数据库
- **消息格式**: JSON
- **传输协议**: HTTP/HTTPS

### 7.2 关键类和方法
- **主处理类**: com.psbc.pfpj.prov3502.xmpldsf.XmplWeb
- **核心方法**: ChangeCard(IKernelServiceContext context)
- **异常处理**: errorCheck(IKernelServiceContext context)
- **数据库操作**: DatabaseExt.queryByNamedSql / executeNamedSql

## 8. 注意事项

1. **数据一致性**: 确保旧卡注销和新卡生效的原子性操作
2. **并发控制**: 避免同一用户同时进行多次换卡操作
3. **日志记录**: 记录关键操作日志便于问题排查
4. **参数校验**: 严格校验输入参数的合法性
5. **状态管理**: 正确维护签约状态的生命周期

## 9. 维护说明

- **配置文件位置**: src/_inbound/prov3502_xmpldsf/XMWEB_XMPLDSF_A004/
- **业务逻辑代码**: src/com/psbc/pfpj/prov3502/xmpldsf/XmplWeb.java
- **SQL配置**: src/com/psbc/pfpj/prov3502/xmpldsf/xmpldsf.namingsqlx
- **消息定义**: src/_inbound/prov3502_xmpldsf/XMWEB_XMPLDSF_A004/
