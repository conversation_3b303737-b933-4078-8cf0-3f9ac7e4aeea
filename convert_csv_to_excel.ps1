# PowerShell脚本：将CSV转换为Excel格式
param(
    [string]$CsvPath = "c:\workspace\psbc-work\xm-xzp-0.0.0.1\xm-xzp-impl\src\main\resources\doc\xzp\缴费明细导出模板.csv",
    [string]$ExcelPath = "c:\workspace\psbc-work\xm-xzp-0.0.0.1\xm-xzp-impl\src\main\resources\doc\xzp\缴费明细导出模板.xls"
)

try {
    # 创建Excel应用程序对象
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # 打开CSV文件
    $workbook = $excel.Workbooks.Open($CsvPath)
    
    # 获取第一个工作表
    $worksheet = $workbook.Worksheets.Item(1)
    $worksheet.Name = "缴费明细"
    
    # 设置表头样式
    $headerRange = $worksheet.Range("A1:J1")
    $headerRange.Font.Bold = $true
    $headerRange.HorizontalAlignment = -4108  # xlCenter
    $headerRange.Interior.Color = 15790320  # 浅灰色背景
    
    # 自动调整列宽
    $worksheet.Columns.AutoFit()
    
    # 保存为Excel格式
    $workbook.SaveAs($ExcelPath, 56)  # 56 = xlExcel8 (.xls format)
    
    # 关闭工作簿和Excel应用程序
    $workbook.Close()
    $excel.Quit()
    
    # 释放COM对象
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($worksheet) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "成功创建Excel模板文件: $ExcelPath"
    
} catch {
    Write-Error "转换失败: $($_.Exception.Message)"
    
    # 确保Excel进程被关闭
    if ($excel) {
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    }
}
