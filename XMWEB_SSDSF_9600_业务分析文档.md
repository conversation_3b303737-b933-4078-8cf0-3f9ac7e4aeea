# XMWEB_SSDSF_9600 交易服务业务分析文档

## 1. 服务概述

### 基本信息
- **交易代码**: XMWEB_SSDSF_9600
- **业务名称**: 实时代收付-手动存款通知
- **业务描述**: 处理手动存款通知业务，用于实时代收付场景下的存款操作
- **传输通道**: bran_prov3502_xmweb
- **业务流程**: _inbound.prov3502_ssdsf.XMWEB_SSDSF_9600.XMWEB_SSDSF_9600
- **创建时间**: 2021-07-07
- **消息格式**: JSON

## 2. 接口定义

### 2.1 请求消息结构 (XMWEB_SSDSF_9600REQ)

#### 消息头 (head) - 基于msg_head_Q
| 字段名 | 显示名称 | 类型 | 长度 | 必填 | 说明 |
|--------|----------|------|------|------|------|
| merchid | 委托单位代码 | String | 12 | 是 | 标识委托单位 |
| opecd | 业务代码 | String | 6 | 是 | 业务类型标识 |
| busikind | 业务类型 | String | 20 | 否 | 业务分类 |
| tradecode | 交易代码 | String | 20 | 是 | 固定值：XMWEB_SSDSF_9600 |
| sendseqno | 发送方流水号 | String | 50 | 是 | 唯一标识本次交易 |
| empname | 账户名称 | String | 50 | 否 | 操作员或账户名称 |
| empcode | 卡号 | String | 30 | 否 | 操作员卡号 |
| orgcode | 机构代码 | String | 9 | 是 | 发起机构代码 |

#### 消息体 (body)
| 字段名 | 显示名称 | 类型 | 必填 | 说明 |
|--------|----------|------|------|------|
| account | 卡号 | String | 是 | 银行卡号 |
| pay_id | 用户号 | String | 是 | 用户标识，包含子业务代码+用户号 |
| sub_ope_cd | 子业务号 | String | 否 | 子业务代码 |

### 2.2 响应消息结构 (XMWEB_SSDSF_9600RES)

#### 消息头 (head) - 基于msg_head_R
| 字段名 | 显示名称 | 类型 | 说明 |
|--------|----------|------|------|
| rescode | 响应代码 | String | 处理结果代码 |
| resmsg | 响应消息 | String | 处理结果描述 |
| resseqno | 响应方流水号 | String | 响应流水号 |
| restime | 响应方时间 | String | 响应时间戳 |
| sendseqno | 发送方流水号 | String | 原请求流水号 |

## 3. 业务逻辑流程

### 3.1 主要处理步骤

```mermaid
graph TD
    A[接收存款通知请求] --> B[提取请求参数]
    B --> C[解析用户号]
    C --> D[拆分子业务代码和用户号]
    D --> E[查询用户签约状态]
    E --> F{用户是否已签约?}
    F -->|是| G[查询子业务参数]
    F -->|否| H[记录签约不存在]
    G --> I[查询委托项目代号]
    I --> J[子业务代码转换]
    J --> K[构造48域数据]
    K --> L[调用主机服务]
    L --> M[返回处理结果]
    H --> N[继续处理]
    N --> G
```

### 3.2 核心业务逻辑

1. **参数提取与验证**
   - 从请求消息中提取委托单位代码(MERCH_ID)、业务代码(OPE_CD)
   - 提取用户号(PAY_ID)、卡号(ACCOUNT)、子业务号(SUB_OPE_CD)

2. **用户号解析**
   - 如果用户号长度大于6位，进行拆分
   - 前6位为子业务代码(tran_type_cd)
   - 后续部分为实际用户号(new_pay_id)
   - 限制用户号最大长度为40位

3. **签约状态验证**
   - 查询tb_pay_commi_info表验证用户签约状态
   - 检查签约状态码，状态为"3"表示签约记录不存在

4. **参数查询与转换**
   - 查询tb_merch_ope_sysparam表获取委托项目代号参数
   - 查询tb_merch_sub_ope表进行子业务代码转换
   - 获取memo4字段作为转换后的子业务代码

5. **48域数据构造**
   - 按照固定格式构造48域数据
   - 格式：PA033930 + 用户号 + 转换后子业务代码 + 填充空格 + 0000
   - 将构造的数据存储到ADD_DATA字段

6. **主机服务调用**
   - 调用common_001主机服务处理业务
   - 传递构造好的报文数据

## 4. 数据库操作

### 4.1 涉及的数据表

#### tb_pay_commi_info (签约信息表)
| 字段名 | 说明 | 类型 |
|--------|------|------|
| merch_id | 委托单位代码 | String |
| ope_cd | 业务代码 | String |
| pay_id | 用户号 | String |
| acc_id | 账户ID | String |
| acc_card_id | 银行卡号 | String |
| stat_cd | 签约状态 | String |
| commi_dt | 签约日期 | String |
| time_stamp | 时间戳 | String |

#### tb_merch_ope_sysparam (委托单位业务系统参数表)
| 字段名 | 说明 | 类型 |
|--------|------|------|
| merch_id | 委托单位代码 | String |
| ope_cd | 业务代码 | String |
| param_cd | 参数代码 | String |
| param_val | 参数值 | String |

#### tb_merch_sub_ope (委托单位子业务表)
| 字段名 | 说明 | 类型 |
|--------|------|------|
| merch_id | 委托单位代码 | String |
| ope_cd | 业务代码 | String |
| merch_inst_id | 委托机构ID | String |
| memo4 | 备注字段4 | String |

### 4.2 SQL操作

#### 查询签约信息
```sql
SELECT merch_id, ope_cd, pay_id, payer_id, acc_id, acc_card_id,
       card_pk_fg, commi_dt, time_stamp, stat_cd
FROM tb_pay_commi_info
WHERE merch_id = ? AND ope_cd = ? AND acc_id = ?
```

#### 查询子业务参数
```sql
SELECT param_val
FROM tb_merch_ope_sysparam
WHERE merch_id = ? AND ope_cd = ? AND param_cd = ?
```

#### 子业务代码转换
```sql
SELECT memo4
FROM tb_merch_sub_ope
WHERE merch_id = ? AND ope_cd = ? AND merch_inst_id = ?
```

## 5. 异常处理

### 5.1 异常处理机制
- **正常流程异常**: 通过error方法处理，返回系统错误
- **业务逻辑异常**: 在主业务方法中记录日志并继续处理
- **数据库异常**: 通过try-catch机制捕获数据库操作异常

### 5.2 错误码定义
| 错误码 | 错误描述 | 处理建议 |
|--------|----------|----------|
| 0000 | 交易成功 | 正常处理完成 |
| 9999 | 系统错误 | 检查系统日志，联系技术支持 |

## 6. 调用示例

### 6.1 请求示例
```json
{
  "head": {
    "merchid": "************",
    "opecd": "ABC001",
    "tradecode": "XMWEB_SSDSF_9600",
    "sendseqno": "20240805001234567890",
    "orgcode": "*********"
  },
  "body": {
    "account": "****************",
    "pay_id": "123456************34567890",
    "sub_ope_cd": "123456"
  }
}
```

### 6.2 响应示例
```json
{
  "head": {
    "rescode": "0000",
    "resmsg": "交易成功",
    "resseqno": "20240805001234567891",
    "restime": "**************",
    "sendseqno": "20240805001234567890"
  }
}
```

## 7. 技术实现

### 7.1 技术架构
- **开发框架**: Primeton BTP
- **数据库**: 关系型数据库
- **消息格式**: JSON
- **传输协议**: HTTP/HTTPS

### 7.2 关键类和方法
- **主处理类**: com.psbc.pfpj.prov3502.xmssdsf.ssdsfManualDeposit
- **核心方法**: DepositNote(IKernelServiceContext context)
- **异常处理**: error(IKernelServiceContext context)
- **数据库操作**: DatabaseExt.queryByNamedSql / executeNamedSql
- **主机服务**: common_001

## 8. 业务特点

### 8.1 核心功能
1. **用户号解析**: 自动拆分复合用户号，提取子业务代码和实际用户号
2. **参数转换**: 根据委托单位和业务代码进行参数查询和转换
3. **报文构造**: 按照银联标准构造48域数据
4. **签约验证**: 验证用户签约状态，确保业务合规性

### 8.2 处理流程
- **前置处理**: 手动存款通知业务逻辑处理
- **主机调用**: 调用common_001主机服务
- **异常处理**: 统一的异常处理机制

## 9. 注意事项

1. **用户号格式**: 用户号需要包含6位子业务代码前缀
2. **参数配置**: 确保tb_merch_ope_sysparam表中参数配置完整
3. **签约状态**: 虽然检查签约状态，但不阻断业务流程
4. **数据长度**: 用户号最大长度限制为40位
5. **48域格式**: 严格按照PA033930+用户号+子业务代码+填充+0000格式

## 10. 维护说明

- **配置文件位置**: src/_inbound/prov3502_ssdsf/XMWEB_SSDSF_9600/
- **业务逻辑代码**: src/com/psbc/pfpj/prov3502/xmssdsf/ssdsfManualDeposit.java
- **SQL配置**: src/com/psbc/pfpj/prov3502/xmssdsf/xmssdsf.namingsqlx
- **消息定义**: src/_inbound/prov3502_ssdsf/XMWEB_SSDSF_9600/
