#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建正确的缴费明细导出模板
"""

import os
import sys

def create_template_manually():
    """手动创建模板文件内容"""
    
    # 读取CSV模板
    csv_path = r'c:\workspace\psbc-work\xm-xzp-0.0.0.1\xm-xzp-impl\src\main\resources\doc\xzp\缴费明细导出模板.csv'
    
    if not os.path.exists(csv_path):
        print(f"CSV文件不存在: {csv_path}")
        return False
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        csv_content = f.read()
    
    print("CSV模板内容:")
    print(csv_content)
    
    # 备份原有的错误模板
    xls_path = r'c:\workspace\psbc-work\xm-xzp-0.0.0.1\xm-xzp-impl\src\main\resources\doc\xzp\缴费明细导出模板.xls'
    backup_path = r'c:\workspace\psbc-work\xm-xzp-0.0.0.1\xm-xzp-impl\src\main\resources\doc\xzp\缴费明细导出模板_backup.xls'
    
    if os.path.exists(xls_path):
        try:
            os.rename(xls_path, backup_path)
            print(f"已备份原模板文件到: {backup_path}")
        except Exception as e:
            print(f"备份文件失败: {e}")
    
    print("\n请手动执行以下步骤:")
    print("1. 打开Excel")
    print("2. 创建新工作簿")
    print("3. 在第一行输入表头:")
    print("   银联清算日期 | 业务代码 | 委托代码 | 子业务代码 | 子业务 | 应付笔数 | 应付金额(元) | 应收笔数 | 应收金额(元) | 轧差金额(元)")
    print("4. 在第二行输入EasyPOI模板标记:")
    print("   {{fe:txnLogDetailList t.setdate}} | {{t.str}} | {{t.merchid}} | {{t.str30}} | {{t.str31}} | {{t.num}} | {{t.amt/100}} | {{t.receivableNum}} | {{t.receivableAmt/100}} | {{t.netAmt}}")
    print("5. 保存为.xls格式到:")
    print(f"   {xls_path}")
    
    return True

if __name__ == '__main__':
    create_template_manually()
