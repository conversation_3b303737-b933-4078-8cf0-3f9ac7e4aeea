# 缴费明细导出问题修复总结

## 问题描述

用户反馈缴费明细导出功能存在问题：
- 前端页面显示的数据结构与导出的Excel文件完全不同
- 前端显示：银联清算日期、业务代码、委托代码、子业务代码、子业务、应付笔数、应付金额(元)、应收笔数、应收金额(元)、轧差金额(元)
- 导出Excel显示：业务代码、委托代码、交易日期、银联清算日期、商户日期、子代码、用户号、中介流水号、卡号/账号、交易代码

## 问题根因

1. **模板文件错误**: `缴费明细导出模板.xls` 实际上是对账清算不一致信息的模板文件
2. **文件大小相同**: 检查发现 `缴费明细导出模板.xls`、`对账清算不一致信息导出模板.xls` 和 `temp_template.xls` 三个文件大小完全相同(20992字节)，说明它们是同一个文件的副本
3. **模板内容错误**: 模板文件包含的是对账清算不一致信息的字段，而不是缴费明细的字段

## 修复方案

### 1. 临时解决方案
- 删除错误的模板文件 `缴费明细导出模板.xls`
- 系统会自动回退到 `createExcelDirectly` 方法
- `createExcelDirectly` 方法的表头是正确的，与前端页面一致

### 2. 长期解决方案
需要重新创建正确的缴费明细导出模板：

#### 模板结构
**表头（第1行）**：
```
银联清算日期 | 业务代码 | 委托代码 | 子业务代码 | 子业务 | 应付笔数 | 应付金额(元) | 应收笔数 | 应收金额(元) | 轧差金额(元)
```

**数据行模板（第2行）**：
```
{{fe:txnLogDetailList t.setdate}} | {{t.str}} | {{t.merchid}} | {{t.str30}} | {{t.str31}} | {{t.num}} | {{t.amt/100}} | {{t.receivableNum}} | {{t.receivableAmt/100}} | {{t.netAmt}}
```

#### 字段映射
- `setdate`: 银联清算日期
- `str`: 业务代码（实际是业务代码_商户号的组合）
- `merchid`: 委托代码（商户号）
- `str30`: 子业务代码（pay_id）
- `str31`: 子业务名称
- `num`: 应付笔数
- `amt`: 应付金额（分，需要除以100转换为元）
- `receivableNum`: 应收笔数
- `receivableAmt`: 应收金额（分，需要除以100转换为元）
- `netAmt`: 轧差金额（元，已计算好）

## 修改的文件

1. **备份错误模板**: `缴费明细导出模板.xls` → `缴费明细导出模板_backup.xls`
2. **删除错误模板**: 删除 `缴费明细导出模板.xls`
3. **代码修改**: 恢复 `IntTxnLogServiceImpl.java` 中的模板导出逻辑

## 验证方法

1. 重新启动后端服务
2. 在前端页面执行导出操作
3. 检查导出的Excel文件表头是否正确
4. 验证数据内容是否与前端页面一致

## 注意事项

1. 模板文件必须是 `.xls` 格式，不能是 `.xlsx`
2. EasyPOI模板语法必须严格按照规范编写
3. 数据字段名必须与Map中的key完全匹配
4. 金额字段需要注意单位转换（分转元）
5. 确保模板文件路径正确且可访问

## 后续工作

1. 重新创建正确的缴费明细导出模板文件
2. 测试模板导出功能
3. 确保导出的Excel格式和样式符合要求
